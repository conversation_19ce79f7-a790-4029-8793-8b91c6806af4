# Chrome MCP Bridge 配置故障排除

## 🚨 当前问题
导入JSON配置时出现 "Failed to save server" 错误

## ✅ 服务状态确认
- ✅ Chrome MCP Bridge服务正在运行 (PID: 11750)
- ✅ 端口12306正常监听
- ✅ 服务器正常响应HTTP请求
- ✅ 返回正确的JSON错误信息

## 🔧 解决方案

### 推荐方案: 手动添加MCP服务器

**不使用Import from JSON，改为手动添加：**

1. **点击 "Add MCP" 按钮**
2. **填写配置信息**：
   ```
   名称: chrome-mcp-bridge
   类型: streamable-http
   URL: http://127.0.0.1:12306/mcp
   ```
3. **保存配置**

### 备选方案1: 修正JSON格式

如果坚持使用Import from JSON，尝试以下配置：

```json
{
  "mcpServers": {
    "chrome-mcp-bridge": {
      "type": "streamable-http",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
```

### 备选方案2: 使用localhost

```json
{
  "mcpServers": {
    "chrome-bridge": {
      "type": "streamable-http", 
      "url": "http://localhost:12306/mcp"
    }
  }
}
```

### 备选方案3: 检查Augment版本兼容性

如果上述方案都不行，可能是Augment版本问题，尝试：

```json
{
  "mcpServers": {
    "chrome-mcp": {
      "command": "mcp-chrome-bridge",
      "args": []
    }
  }
}
```

## 🔍 可能的错误原因

1. **JSON格式问题**: Augment对JSON格式要求严格
2. **服务器名称冲突**: 可能与现有配置冲突
3. **URL格式问题**: 某些版本可能不支持127.0.0.1
4. **权限问题**: Augment可能没有访问本地服务的权限

## 📝 建议的操作步骤

1. **首先尝试手动添加** (最可靠的方法)
2. **如果手动添加失败，检查Chrome扩展状态**
3. **确认mcp-chrome-bridge服务正在运行**
4. **重启Augment应用**
5. **如果仍有问题，尝试重启Chrome MCP Bridge服务**

## 🚀 验证配置成功

配置成功后应该看到：
- MCP服务器列表中出现chrome-mcp-bridge
- 状态显示为"已连接"或绿色
- 可以在对话中使用Chrome MCP功能

## 💡 下一步

建议您先尝试**手动添加MCP服务器**的方法，这通常是最可靠的配置方式。
