// 测试Chrome MCP连接的脚本
const http = require('http');

function testMCPConnection() {
    const options = {
        hostname: '127.0.0.1',
        port: 12306,
        path: '/mcp',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    };

    const req = http.request(options, (res) => {
        console.log(`状态码: ${res.statusCode}`);
        console.log(`响应头: ${JSON.stringify(res.headers)}`);
        
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log('响应内容:', data);
            if (res.statusCode === 400 && data.includes('Invalid or missing MCP session ID')) {
                console.log('✅ Chrome MCP服务器正在正常运行！');
                console.log('这个400错误是正常的，因为我们没有提供MCP会话ID');
            }
        });
    });

    req.on('error', (e) => {
        console.error(`请求出错: ${e.message}`);
    });

    req.end();
}

console.log('正在测试Chrome MCP连接...');
testMCPConnection();
