# Chrome MCP 扩展ID检查

## 当前注册的扩展ID
从mcp-chrome-bridge注册信息看到的扩展ID是：
`chrome-extension://hbdgbgagpkpjffpklnamcljpakneikee/`

## 如何检查你的Chrome扩展ID

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 找到"Chrome MCP Server"扩展
4. 查看扩展ID（在扩展名称下方）

## 如果扩展ID不匹配

如果你的Chrome扩展ID与 `hbdgbgagpkpjffpklnamcljpakneikee` 不匹配，需要重新注册：

```bash
mcp-chrome-bridge register --extension-id YOUR_ACTUAL_EXTENSION_ID
```

## 配置说明

使用以下两种配置之一：

### 配置1: 使用命令名
```json
{
  "mcpServers": {
    "chrome-mcp-server": {
      "command": "mcp-chrome-bridge",
      "args": []
    }
  }
}
```

### 配置2: 使用完整路径
```json
{
  "mcpServers": {
    "chrome-mcp-server": {
      "command": "/Users/<USER>/Library/pnpm/mcp-chrome-bridge",
      "args": []
    }
  }
}
```
