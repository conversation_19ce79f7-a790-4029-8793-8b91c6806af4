#!/usr/bin/env node

/**
 * Chrome MCP Bridge 连接测试脚本
 * 用于验证Chrome MCP服务器是否正常运行
 */

const http = require('http');
const https = require('https');

// MCP服务器配置
const MCP_CONFIG = {
    host: '127.0.0.1',
    port: 12306,
    path: '/mcp',
    timeout: 5000
};

/**
 * 测试HTTP连接
 */
function testHttpConnection() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: MCP_CONFIG.host,
            port: MCP_CONFIG.port,
            path: MCP_CONFIG.path,
            method: 'GET',
            timeout: MCP_CONFIG.timeout
        };

        console.log(`🔍 测试连接: http://${MCP_CONFIG.host}:${MCP_CONFIG.port}${MCP_CONFIG.path}`);

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    success: true,
                    statusCode: res.statusCode,
                    headers: res.headers,
                    data: data
                });
            });
        });

        req.on('error', (error) => {
            reject({
                success: false,
                error: error.message,
                code: error.code
            });
        });

        req.on('timeout', () => {
            req.destroy();
            reject({
                success: false,
                error: '连接超时',
                code: 'TIMEOUT'
            });
        });

        req.end();
    });
}

/**
 * 检查Chrome扩展状态
 */
function checkChromeExtension() {
    console.log('\n📋 Chrome扩展检查清单:');
    console.log('1. ✅ 打开 Chrome 浏览器');
    console.log('2. ✅ 访问 chrome://extensions/');
    console.log('3. ✅ 确认"开发者模式"已启用');
    console.log('4. ✅ 确认Chrome MCP扩展已加载并启用');
    console.log('5. ✅ 点击扩展图标，确认显示"连接"按钮');
    console.log('6. ✅ 点击"连接"按钮，确认连接成功');
}

/**
 * 检查MCP桥接服务
 */
function checkMcpBridge() {
    console.log('\n🌉 MCP桥接服务检查:');
    console.log('1. ✅ 确认已全局安装: npm install -g mcp-chrome-bridge');
    console.log('2. ✅ 确认服务正在运行在端口 12306');
    console.log('3. ✅ 确认防火墙允许本地连接');
}

/**
 * 主测试函数
 */
async function runTests() {
    console.log('🚀 Chrome MCP Bridge 连接测试开始\n');
    console.log('=' .repeat(50));

    // 检查预置条件
    checkChromeExtension();
    checkMcpBridge();

    console.log('\n' + '=' .repeat(50));
    console.log('🔗 开始连接测试...\n');

    try {
        // 测试HTTP连接
        const result = await testHttpConnection();
        
        if (result.success) {
            console.log('✅ MCP服务器连接成功!');
            console.log(`   状态码: ${result.statusCode}`);
            console.log(`   响应头: ${JSON.stringify(result.headers, null, 2)}`);
            
            if (result.data) {
                console.log(`   响应数据: ${result.data.substring(0, 200)}...`);
            }
            
            console.log('\n🎉 Chrome MCP Bridge 服务运行正常!');
            console.log('📝 可以开始使用以下功能:');
            console.log('   • 浏览器窗口和标签页管理');
            console.log('   • 网页截图和内容提取');
            console.log('   • 网络请求监控和分析');
            console.log('   • AI驱动的语义搜索');
            console.log('   • 元素交互和表单操作');
            console.log('   • 浏览历史和书签管理');
            
        } else {
            console.log('❌ MCP服务器连接失败');
        }
        
    } catch (error) {
        console.log('❌ 连接测试失败:');
        console.log(`   错误: ${error.error}`);
        console.log(`   代码: ${error.code}`);
        
        console.log('\n🔧 故障排除建议:');
        console.log('1. 确认Chrome MCP扩展已正确安装和启用');
        console.log('2. 确认mcp-chrome-bridge服务正在运行');
        console.log('3. 检查端口12306是否被其他程序占用');
        console.log('4. 尝试重启Chrome浏览器和MCP服务');
        console.log('5. 检查防火墙设置是否阻止本地连接');
    }

    console.log('\n' + '=' .repeat(50));
    console.log('测试完成');
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testHttpConnection,
    checkChromeExtension,
    checkMcpBridge,
    runTests
};
