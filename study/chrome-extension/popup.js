// Chrome MCP Server Popup Script
document.addEventListener('DOMContentLoaded', function() {
  const statusDiv = document.getElementById('status');
  const testButton = document.getElementById('testConnection');
  const pageInfoButton = document.getElementById('getPageInfo');
  
  // Test connection button
  testButton.addEventListener('click', function() {
    chrome.runtime.sendMessage({ action: 'testConnection' }, function(response) {
      if (response && response.success) {
        updateStatus('connected', '状态: 已连接');
      } else {
        updateStatus('disconnected', '状态: 连接失败');
      }
    });
  });
  
  // Get page info button
  pageInfoButton.addEventListener('click', function() {
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, { action: 'getPageInfo' }, function(response) {
        if (response && response.success) {
          console.log('Page info:', response.data);
          alert('页面信息已输出到控制台');
        } else {
          alert('获取页面信息失败');
        }
      });
    });
  });
  
  function updateStatus(className, text) {
    statusDiv.className = 'status ' + className;
    statusDiv.textContent = text;
  }
  
  // Check initial connection status
  chrome.runtime.sendMessage({ action: 'getConnectionStatus' }, function(response) {
    if (response && response.connected) {
      updateStatus('connected', '状态: 已连接');
    } else {
      updateStatus('disconnected', '状态: 未连接');
    }
  });
});
