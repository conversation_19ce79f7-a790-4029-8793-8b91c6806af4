// Chrome MCP Server Background Script
let nativePort = null;

// Connect to native messaging host
function connectToNativeHost() {
  try {
    nativePort = chrome.runtime.connectNative('com.chromemcp.nativehost');
    
    nativePort.onMessage.addListener((message) => {
      console.log('Received message from native host:', message);
      handleNativeMessage(message);
    });
    
    nativePort.onDisconnect.addListener(() => {
      console.log('Native host disconnected');
      if (chrome.runtime.lastError) {
        console.error('Native host error:', chrome.runtime.lastError.message);
      }
      nativePort = null;
      // Try to reconnect after a delay
      setTimeout(connectToNativeHost, 5000);
    });
    
    console.log('Connected to native host');
    
    // Send initial connection message
    nativePort.postMessage({
      type: 'connection',
      status: 'connected',
      timestamp: Date.now()
    });
    
  } catch (error) {
    console.error('Failed to connect to native host:', error);
    setTimeout(connectToNativeHost, 5000);
  }
}

// Handle messages from native host
function handleNativeMessage(message) {
  switch (message.type) {
    case 'navigate':
      handleNavigate(message);
      break;
    case 'click':
      handleClick(message);
      break;
    case 'type':
      handleType(message);
      break;
    case 'screenshot':
      handleScreenshot(message);
      break;
    case 'getPageInfo':
      handleGetPageInfo(message);
      break;
    default:
      console.log('Unknown message type:', message.type);
  }
}

// Handle navigation
async function handleNavigate(message) {
  try {
    const tab = await getCurrentTab();
    await chrome.tabs.update(tab.id, { url: message.url });
    sendResponse(message.id, { success: true, url: message.url });
  } catch (error) {
    sendResponse(message.id, { success: false, error: error.message });
  }
}

// Handle click
async function handleClick(message) {
  try {
    const tab = await getCurrentTab();
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: clickElement,
      args: [message.selector]
    });
    sendResponse(message.id, { success: true });
  } catch (error) {
    sendResponse(message.id, { success: false, error: error.message });
  }
}

// Handle typing
async function handleType(message) {
  try {
    const tab = await getCurrentTab();
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: typeText,
      args: [message.selector, message.text]
    });
    sendResponse(message.id, { success: true });
  } catch (error) {
    sendResponse(message.id, { success: false, error: error.message });
  }
}

// Handle screenshot
async function handleScreenshot(message) {
  try {
    const tab = await getCurrentTab();
    const dataUrl = await chrome.tabs.captureVisibleTab(tab.windowId, { format: 'png' });
    sendResponse(message.id, { success: true, screenshot: dataUrl });
  } catch (error) {
    sendResponse(message.id, { success: false, error: error.message });
  }
}

// Handle get page info
async function handleGetPageInfo(message) {
  try {
    const tab = await getCurrentTab();
    const results = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: getPageInfo
    });
    sendResponse(message.id, { success: true, pageInfo: results[0].result });
  } catch (error) {
    sendResponse(message.id, { success: false, error: error.message });
  }
}

// Helper functions for content script injection
function clickElement(selector) {
  const element = document.querySelector(selector);
  if (element) {
    element.click();
    return true;
  }
  return false;
}

function typeText(selector, text) {
  const element = document.querySelector(selector);
  if (element) {
    element.value = text;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }
  return false;
}

function getPageInfo() {
  return {
    title: document.title,
    url: window.location.href,
    html: document.documentElement.outerHTML
  };
}

// Get current active tab
async function getCurrentTab() {
  const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
  return tabs[0];
}

// Send response back to native host
function sendResponse(messageId, response) {
  if (nativePort) {
    nativePort.postMessage({
      id: messageId,
      ...response
    });
  }
}

// Initialize connection when extension starts
chrome.runtime.onStartup.addListener(connectToNativeHost);
chrome.runtime.onInstalled.addListener(connectToNativeHost);

// Connect immediately
connectToNativeHost();
