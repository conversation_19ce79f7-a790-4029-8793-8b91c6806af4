{"manifest_version": 3, "name": "Chrome MCP Server", "version": "1.0.0", "description": "Chrome MCP Server Extension for browser automation", "permissions": ["nativeMessaging", "activeTab", "tabs", "storage", "scripting"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_end"}], "action": {"default_popup": "popup.html", "default_title": "Chrome MCP Server"}}