// Chrome MCP Server Content Script
console.log('Chrome MCP Server content script loaded');

// Listen for messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Content script received message:', request);
  
  switch (request.action) {
    case 'click':
      const clickResult = clickElement(request.selector);
      sendResponse({ success: clickResult });
      break;
      
    case 'type':
      const typeResult = typeText(request.selector, request.text);
      sendResponse({ success: typeResult });
      break;
      
    case 'getPageInfo':
      sendResponse({
        success: true,
        data: {
          title: document.title,
          url: window.location.href,
          html: document.documentElement.outerHTML
        }
      });
      break;
      
    default:
      sendResponse({ success: false, error: 'Unknown action' });
  }
  
  return true; // Keep message channel open for async response
});

function clickElement(selector) {
  try {
    const element = document.querySelector(selector);
    if (element) {
      element.click();
      return true;
    }
    return false;
  } catch (error) {
    console.error('Click error:', error);
    return false;
  }
}

function typeText(selector, text) {
  try {
    const element = document.querySelector(selector);
    if (element) {
      element.value = text;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
    return false;
  } catch (error) {
    console.error('Type error:', error);
    return false;
  }
}
