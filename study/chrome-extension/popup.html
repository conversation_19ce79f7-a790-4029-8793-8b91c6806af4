<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 10px;
    }
    .connected {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .disconnected {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background-color: #0056b3;
    }
  </style>
</head>
<body>
  <h3>Chrome MCP Server</h3>
  <div id="status" class="status disconnected">
    状态: 未连接
  </div>
  <button id="testConnection">测试连接</button>
  <button id="getPageInfo">获取页面信息</button>
  
  <script src="popup.js"></script>
</body>
</html>
