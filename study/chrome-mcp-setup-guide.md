# Chrome MCP Bridge 完整配置指南

## 🎯 配置步骤

### 1. 确认服务安装状态

首先确认您已经安装了Chrome MCP Bridge：

```bash
# 检查是否已全局安装
npm list -g mcp-chrome-bridge

# 如果没有安装，请运行：
npm install -g mcp-chrome-bridge
```

### 2. 启动Chrome MCP Bridge服务

在终端中运行：

```bash
# 启动服务（默认端口12306）
mcp-chrome-bridge

# 或者指定端口
PORT=12306 mcp-chrome-bridge
```

### 3. 确认Chrome扩展状态

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 确认"开发者模式"已启用
4. 确认Chrome MCP扩展已加载并启用
5. 点击扩展图标，点击"连接"按钮

### 4. 在Augment中配置MCP

使用以下JSON配置：

```json
{
  "mcpServers": {
    "chrome-mcp-server": {
      "type": "streamable-http",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
```

### 5. 配置导入方法

#### 方法1: 使用"Import from JSON"按钮
1. 点击Augment设置中的"Import from JSON"按钮
2. 将上面的JSON配置粘贴进去
3. 点击导入

#### 方法2: 手动添加
1. 点击"Add MCP"按钮
2. 选择"streamable-http"类型
3. 输入名称：`chrome-mcp-server`
4. 输入URL：`http://127.0.0.1:12306/mcp`
5. 保存配置

## 🔧 故障排除

### 常见问题1: 连接失败
**症状**: 无法连接到MCP服务器
**解决方案**:
1. 确认mcp-chrome-bridge服务正在运行
2. 检查端口12306是否被占用
3. 确认防火墙设置允许本地连接

### 常见问题2: Chrome扩展未连接
**症状**: 扩展显示未连接状态
**解决方案**:
1. 重新加载Chrome扩展
2. 点击扩展图标，手动点击"连接"
3. 重启Chrome浏览器

### 常见问题3: MCP配置错误
**症状**: Augment无法识别MCP服务器
**解决方案**:
1. 确认使用"streamable-http"类型而不是"command"类型
2. 确认URL格式正确：`http://127.0.0.1:12306/mcp`
3. 重启Augment应用

## ✅ 验证配置成功

配置成功后，您应该能够：
1. 在Augment中看到chrome-mcp-server服务状态为"已连接"
2. 可以使用Chrome MCP的20+个工具
3. 能够控制和操作您的Chrome浏览器

## 🚀 测试功能

配置完成后，可以尝试以下测试：
1. "用Chrome MCP截图当前页面"
2. "用Chrome MCP列出所有打开的标签页"
3. "用Chrome MCP搜索浏览器中的内容"
